<?php

namespace Tests\Unit\Http\Middleware;

use App\Apomail;
use App\Enums\ApomailStatus;
use App\Http\Middleware\CheckApomailRegistration;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route;
use Tests\TestCase;

class CheckApomailRegistrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Route::get('/dummy-test-route', static fn () => 'nice')
            ->middleware([CheckApomailRegistration::class])
            ->name('dummy-test-route');
    }

    public function test_it_redirects_correctly()
    {
        [$owner, $pharmacy] = $this->createPharmacyUserWithPharmacy();
        $user = $this->createPharmacyEmployee($pharmacy);
        $this->createLocalStripeSubscription($pharmacy);

        $user->update([
            'email' => null,
            'username' => 'username',
        ]);

        $this->actingAs($user);

        $this->get('/dummy-test-route')
            ->assertOk();

        $apomail = Apomail::forceCreate(Apomail::factory()->raw([
            'owner_id' => $owner->id,
            'status' => ApomailStatus::RESERVED,
        ]));

        $user->usingApomails()->attach($apomail);

        $this->get('/dummy-test-route')
            ->assertRedirectToRoute('apo-mail-registration');

        // It caches that it has shown the page for 12 hours
        $this->get('/dummy-test-route')
            ->assertOk();

        $this->travelTo(now()->addHours(13));

        $this->get('/dummy-test-route')
            ->assertRedirectToRoute('apo-mail-registration');

        $this->travelTo(now()->addHours(26));

        $apomail->update([
            'status' => ApomailStatus::ACTIVE,
        ]);

        $this->get('/dummy-test-route')
            ->assertOk();
    }
}
