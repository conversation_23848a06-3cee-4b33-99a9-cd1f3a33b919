<?php

namespace Tests\Unit\Domains\Subscription\Application\Jobs;

use App\Domains\Subscription\Application\Jobs\OffboardCardlinkJob;
use App\Enums\CardLink\CardLinkOrderStatusEnum;
use App\Enums\CardLink\CardLinkPackageEnum;
use App\Http\Integrations\CardLinkService\Requests\GetCardLinkSettingsRequest;
use App\Processes\OffboardCardLink;
use Saloon\Http\Faking\MockClient;
use Saloon\Http\Faking\MockResponse;
use Tests\TestCase;

class OffboardCardlinkJobTest extends TestCase
{
    public function test_it_offboards_cardlink(): void
    {
        $pharmacy = $this->createPharmacy();

        $job = new OffboardCardlinkJob($pharmacy);

        $this->mock(OffboardCardLink::class, function ($mock) {
            $mock->shouldReceive('run')->never();
        });

        $job->handle();

        $pharmacyWithCardlink = $this->createPharmacy();
        $pharmacyWithCardlink->cardLinkOrder()->create([
            'order_id' => 1,
            'status' => CardLinkOrderStatusEnum::Activated,
            'user_id' => 1,
            'order_information' => [
                'package' => CardLinkPackageEnum::Unlimited,
                'transmitted_at' => now()->toIso8601String(),
            ],
        ]);

        $jobWithIaIntegration = new OffboardCardlinkJob($pharmacyWithCardlink);

        $this->mock(OffboardCardLink::class, function ($mock) {
            $mock->shouldReceive('run')->once();
        });

        $jobWithIaIntegration->handle();
    }

    public function test_runtime_exception(): void
    {
        $pharmacy = $this->createPharmacy();
        $pharmacy->cardLinkOrder()->create([
            'order_id' => 1,
            'status' => CardLinkOrderStatusEnum::Activated,
            'user_id' => 1,
            'order_information' => [
                'package' => CardLinkPackageEnum::Unlimited,
                'transmitted_at' => now()->toIso8601String(),
            ],
        ]);
        $job = new OffboardCardlinkJob($pharmacy);

        $this->mock(OffboardCardLink::class, function ($mock) {
            $mock->shouldReceive('run')->andThrow(new \Exception('Test exception'));
        });

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Couldnt offboard cardlink for pharmacy: '.$pharmacy->id);

        $job->handle();
    }

    public function test_api_error(): void
    {
        $pharmacy = $this->createPharmacy();
        $pharmacy->cardLinkOrder()->create([
            'order_id' => 1,
            'status' => CardLinkOrderStatusEnum::Activated,
            'user_id' => 1,
            'order_information' => [
                'package' => CardLinkPackageEnum::Unlimited,
                'transmitted_at' => now()->toIso8601String(),
            ],
        ]);
        $job = new OffboardCardlinkJob($pharmacy);

        $this->mock(OffboardCardLink::class, function ($mock) {
            $mock->shouldReceive('run')->never();
        });

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Couldnt offboard cardlink for pharmacy: '.$pharmacy->id);
        MockClient::destroyGlobal();

        MockClient::global([GetCardLinkSettingsRequest::class => MockResponse::make($this->getCardLinkSettingsRequestData(250, 250), 500)]);
        $job->handle();
    }
}
