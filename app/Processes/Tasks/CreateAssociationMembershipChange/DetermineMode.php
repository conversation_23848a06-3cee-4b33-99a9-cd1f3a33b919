<?php

namespace App\Processes\Tasks\CreateAssociationMembershipChange;

use App\Enums\AssociationMembershipChangeModeEnum;
use App\Exceptions\AssociationMembershipChangeException;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use Closure;

class DetermineMode
{
    public function __invoke(CreateAssociationMembershipChangePayload $payload, Closure $next): CreateAssociationMembershipChangePayload
    {
        $payload->mode = $payload->mode ?? match (true) {
            $payload->old === null && $payload->new !== null => AssociationMembershipChangeModeEnum::ADD,
            $payload->new === null && $payload->old !== null => AssociationMembershipChangeModeEnum::DELETE,
            $payload->old !== null && $payload->new !== null && $payload->old !== $payload->new => AssociationMembershipChangeModeEnum::CHANGE,
            default => throw AssociationMembershipChangeException::invalidState('both old and new cannot be null or identical')
        };

        return $next($payload);
    }
}
