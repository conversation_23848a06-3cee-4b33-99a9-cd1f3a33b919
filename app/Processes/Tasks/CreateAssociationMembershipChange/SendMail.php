<?php

namespace App\Processes\Tasks\CreateAssociationMembershipChange;

use App\Enums\AssociationMembershipChangeModeEnum;
use App\Mail\ChangeAssociationMembershipUserInfoMail;
use App\Mail\RevertAssociationMembershipUserInfoMail;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use Closure;
use Illuminate\Support\Facades\Mail;

class SendMail
{
    public function __invoke(CreateAssociationMembershipChangePayload $payload, Closure $next): CreateAssociationMembershipChangePayload
    {
        $new = is_int($payload->new) ? $payload->new : $payload->new?->id;

        $mail = match ($payload->mode) {
            AssociationMembershipChangeModeEnum::DELETE => new ChangeAssociationMembershipUserInfoMail($payload->user, $new, $payload->changeAt),
            AssociationMembershipChangeModeEnum::CHANGE => new ChangeAssociationMembershipUserInfoMail($payload->user, $new, $payload->changeAt),
            AssociationMembershipChangeModeEnum::ADD => new ChangeAssociationMembershipUserInfoMail($payload->user, $new, $payload->changeAt),
            AssociationMembershipChangeModeEnum::REVERT => new RevertAssociationMembershipUserInfoMail($payload->user, $new),
            default => throw new \Exception('Cannot process association membership change mode: '.$payload->mode->value),
        };

        Mail::to($payload->user)->queue($mail);

        return $next($payload);
    }
}
