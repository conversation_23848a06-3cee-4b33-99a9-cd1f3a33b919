<?php

namespace App\Nova\Actions;

use App\AssociationMembershipChange;
use App\Enums\AssociationMembershipChangeModeEnum;
use App\Processes\CreateAssociationMembershipChange;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Field;
use Laravel\Nova\Fields\Heading;
use Laravel\Nova\Http\Requests\NovaRequest;

class RevertLastAssociationMembershipChangeAction extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Letzte Änderung rückgängig machen';

    public function handle(ActionFields $fields, Collection $models): mixed
    {
        if ($models->isEmpty()) {
            $models->add(NovaRequest::createFrom(request())->findParentModelOrFail());
        }

        $failedModels = collect();

        /** @var Collection<User> $models */
        foreach ($models as $model) {
            $associationMembershipChange = $model->associationMembershipChanges()
                ->whereNot('mode', AssociationMembershipChangeModeEnum::REVERT)
                ->latest()
                ->first();

            if ($this->canRevertLastAssociationMembershipChange($model, $associationMembershipChange)) {
                if ($associationMembershipChange?->change_done_at === null) {
                    $associationMembershipChange?->touch('canceled_at');
                }

                $payload = new CreateAssociationMembershipChangePayload(
                    user: $model,
                    changeAt: now(),
                    old: $associationMembershipChange?->association_id_after,
                    new: $associationMembershipChange?->association_id_before,
                    mode: AssociationMembershipChangeModeEnum::REVERT,
                    changeDoneAt: $associationMembershipChange?->change_done_at === null ? now() : null,
                );
                $process = new CreateAssociationMembershipChange;
                $process->run($payload);
            } else {
                $failedModels->add($model);
            }
        }

        if ($failedModels->isNotEmpty()) {
            return Action::danger('Die letzte Änderung kann nicht rückgängig gemacht werden.');
        }

        return Action::message('Die Aktion war erfolgreich');
    }

    public function canRevertLastAssociationMembershipChange(User $user, ?AssociationMembershipChange $associationMembershipChange = null): bool
    {
        if ($user->associationMembershipChanges()->whereNot('mode', AssociationMembershipChangeModeEnum::REVERT)->count() === 0) {
            return false;
        }

        $latestAssociationMembershipChange = $user->associationMembershipChanges()
            ->latest()
            ->first();

        if ($associationMembershipChange?->isNot($latestAssociationMembershipChange)) {
            return false;
        }

        return true;
    }

    /**
     * @return array<Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            Heading::make('Bitte die Dokumentation konsultieren, dort sind alle wichtigen Informationen enthalten: <a target="_blank" href="https://gedisa.atlassian.net/wiki/spaces/A/pages/1084424201/Feature+Dokumentation+f+r+die+Mitgliederverwaltung+Verbandseintritt+und+Verbandsaustritt#Vorgemerkte-Verbands%C3%A4nderung-r%C3%BCckg%C3%A4ngig-machen"><strong>Hier klicken (öffnet im neuen Tab)</strong></a>')->asHtml(),
        ];
    }
}
