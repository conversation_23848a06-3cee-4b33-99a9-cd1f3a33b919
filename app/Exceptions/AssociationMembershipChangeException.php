<?php

namespace App\Exceptions;

use Exception;

class AssociationMembershipChangeException extends Exception
{
    public static function invalidMode(string $mode): self
    {
        return new self("Invalid association membership change mode: {$mode}");
    }

    public static function invalidState(string $message): self
    {
        return new self("Invalid association membership change state: {$message}");
    }

    public static function processingFailed(string $reason, ?\Throwable $previous = null): self
    {
        return new self("Association membership change processing failed: {$reason}", 0, $previous);
    }
}
