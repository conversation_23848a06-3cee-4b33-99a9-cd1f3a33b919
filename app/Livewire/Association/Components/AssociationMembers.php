<?php

namespace App\Livewire\Association\Components;

use App\Association;
use App\Domains\Association\Domain\Data\AssociationMemberListEntry;
use App\Domains\Association\Domain\Enums\AssociationMemberListEntryStatus;
use App\Excel\Imports\AssociationMemberImport;
use App\MemberImport;
use App\Processes\CreateAssociationMembershipChange;
use App\Processes\Payloads\CreateAssociationMembershipChangePayload;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\File;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\HeadingRowImport;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;
use Maatwebsite\Excel\Validators\Failure;
use Maatwebsite\Excel\Validators\ValidationException as ExcelValidationException;
use RuntimeException;

class AssociationMembers extends Component
{
    use WithFileUploads;

    public Association $association;

    public $search = '';

    public $sortBy = 'created_at';

    public $sortOrder = 'desc';

    public $file = null;

    public bool $modalOpen = false;

    public ?string $removeMemberDate = null;

    public ?User $userToRemove;

    public ?string $memberImportIdToRemove = null;

    public const MODAL_NAME = 'add-association-members';

    public const ALLOWED_FILE_TYPES = [
        '.xls' => 'application/vnd.ms-excel',
        '.xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];

    public function rules(): array
    {
        return [
            'association' => 'required',
            'modalOpen' => 'boolean',
        ];
    }

    public function updated(string $propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function changeSorting(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortOrder = $this->sortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
        }
    }

    public function removeFile(): void
    {
        $this->file = null;
    }

    public function addMembers(): void
    {
        $hasErrors = $this->validateFile();

        if ($hasErrors) {
            return;
        }

        $path = $this->file->storeAs('temp', $this->file->getClientOriginalName(), 'local');

        $this->validateHeader($path);

        if ($this->getErrorBag()->count() > 0) {
            $this->showErrorsClearStorage($path);

            return;
        }

        $this->validateAndImportContent($path);

        if ($this->getErrorBag()->count() > 0) {
            return;
        }

        $this->modalOpen = false;
        $this->dispatch('close-modal');
        $this->openModal('successfully-imported');
    }

    public function validateHeader(string $path): void
    {
        HeadingRowFormatter::default('none');

        $this->validateFirstHeaderRow($path);
        $this->validateSecondHeaderRow($path);

        HeadingRowFormatter::default('slug');
    }

    public function validateFirstHeaderRow(string $path): void
    {
        $headingsFirstRow = Arr::flatten((new HeadingRowImport)->toArray($path));

        if (! Arr::has($headingsFirstRow, 7) || $headingsFirstRow[7] !== 'Adresse der Apotheke') {
            $this->addError('file', 'Der Spaltenname "Adresse der Apotheke" muss ab H1 vorhanden sein.');
        }
    }

    public function validateSecondHeaderRow(string $path): void
    {
        $headingsSecondRow = Arr::flatten((new HeadingRowImport(2))->toArray($path));

        if (! Arr::has($headingsSecondRow, 0) || $headingsSecondRow[0] !== 'Anrede (Auswahlfeld)') {
            $this->addError('file', 'Der Spaltenname "Anrede (Auswahlfeld)" muss in A2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 1) || $headingsSecondRow[1] !== 'Akadem. Grad') {
            $this->addError('file', 'Der Spaltenname "Akadem. Grad" muss in B2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 2) || $headingsSecondRow[2] !== 'Vorname Inhaber') {
            $this->addError('file', 'Der Spaltenname "Vorname Inhaber" muss in C2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 3) || $headingsSecondRow[3] !== 'Nachname Inhaber') {
            $this->addError('file', 'Der Spaltenname "Nachname Inhaber" muss in D2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 4) || $headingsSecondRow[4] !== 'E-Mail-Adresse Inhaber') {
            $this->addError('file', 'Der Spaltenname "E-Mail-Adresse Inhaber" muss in E2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 5) || $headingsSecondRow[5] !== 'OHG Name') {
            $this->addError('file', 'Der Spaltenname "OHG Name" muss in F2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 6) || $headingsSecondRow[6] !== 'Name der Apotheke') {
            $this->addError('file', 'Der Spaltenname "Name der Apotheke" muss in G2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 7) || $headingsSecondRow[7] !== 'Adresszusatz') {
            $this->addError('file', 'Der Spaltenname "Adresszusatz" muss in H2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 8) || $headingsSecondRow[8] !== 'Straße + Hausnr.') {
            $this->addError('file', 'Der Spaltenname "Straße + Hausnr." muss in I2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 9) || $headingsSecondRow[9] !== 'Postleitzahl') {
            $this->addError('file', 'Der Spaltenname "Postleitzahl" muss in J2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 10) || $headingsSecondRow[10] !== 'Ort') {
            $this->addError('file', 'Der Spaltenname "Ort" muss in K2 vorhanden sein.');
        }
        if (! Arr::has($headingsSecondRow, 11) || $headingsSecondRow[11] !== 'Datum (Format: TT.MM.YYYY)') {
            $this->addError('file', 'Der Spaltenname "Datum (Format: TT.MM.YYYY)" muss in L2 vorhanden sein.');
        }
    }

    public function validateAndImportContent(string $path): void
    {
        try {
            Log::channel('association-member-import')->info('Association Member excel import started', ['user' => user()?->uuid ?? user()?->id ?? 'N/A', 'file' => $path]);
            (new AssociationMemberImport($this->association->id))->import($path);
        } catch (ExcelValidationException $e) {
            $this->modalOpen = true;
            Log::channel('association-member-import')->info('Association Member excel import failed validation', ['reason' => $e->getMessage(), 'user' => user()?->uuid ?? user()?->id ?? 'N/A', 'file' => $path]);
            $this->setErrorMessages($e);
        } finally {
            $this->deleteFileFromStorage($path);
        }

        Log::channel('association-member-import')->info('Association Member excel import finished', ['user' => user()?->uuid ?? user()?->id ?? 'N/A', 'file' => $path]);
    }

    public function deleteFileFromStorage(string $path): void
    {
        if (isset($path)) {
            Storage::delete($path);
        }
    }

    private function validateFile(): bool
    {
        try {
            Validator::make(['file' => $this->file], [
                'file' => ['required', File::types(array_values(self::ALLOWED_FILE_TYPES))],
            ], [
                'file.required' => 'Sie müssen mindestens eine Datei hochladen.',
            ])->validate();
        } catch (ValidationException $e) {
            $this->modalOpen = true;
            $this->addError('file', $e->errors()['file'][0]);

            return true;
        }

        return false;
    }

    private function setErrorMessages(ExcelValidationException $e): void
    {
        collect($e->failures())->each(function (Failure $failure) {
            collect($failure->errors())->each(function (string $error) use ($failure) {
                $this->addError(
                    'file',
                    'Zeile '.$failure->row().': '.$error
                );
            });
        });
    }

    private function showErrorsClearStorage(string $path): void
    {
        $this->modalOpen = true;
        $this->deleteFileFromStorage($path);
    }

    public function getRemoveMemberDateOptions(): Collection
    {
        $options = collect();

        $period = CarbonPeriod::create(
            now()->endOfQuarter(),
            '1 quarter',
            Carbon::now()->addQuartersNoOverflow(5)
        );

        foreach ($period as $dt) {
            assert($dt instanceof Carbon);
            $dt = $dt->endOfMonth();
            $options->put($dt->format('Y-m-d'), $dt->day.'. '.__('months.'.$dt->format('n')).' '.$dt->format('Y'));
        }

        return $options->sortKeys();
    }

    public function removeAssociationMember(int $memberId): void
    {
        $success = false;

        $this->validateOnly('removeMemberDate', [
            'removeMemberDate' => ['required'],
        ], [
            'removeMemberDate.required' => 'Das Austrittsdatum muss ausgefüllt werden.',
        ]);

        try {
            if ($this->userToRemove === null || $this->userToRemove->id !== $memberId) {
                throw new RuntimeException('No user selected to remove.');
            }

            if (empty($this->removeMemberDate)) {
                throw new RuntimeException('No remove date selected.');
            }

            assert($this->userToRemove instanceof User);

            $payload = new CreateAssociationMembershipChangePayload(
                user: $this->userToRemove,
                changeAt: Carbon::parse($this->removeMemberDate)->endOfMonth(),
                old: $this->userToRemove->pharmacyProfile?->association_id,
            );

            $process = new CreateAssociationMembershipChange;
            $success = (bool) $process->run($payload);
        } catch (\Throwable $exception) {
            report($exception);
        }

        $this->userToRemove = null;
        $this->dispatch('close-modal');

        /** @phpstan-ignore-next-line */
        $this->openModal($success ? 'successfully-deleted-association-member' : 'failed-to-delete-association-member');
    }

    public function removeMember(string $memberId): bool
    {
        if (empty($memberId)) {
            return false;
        }

        $this->userToRemove = User::find($memberId);
        $this->removeMemberDate = now()->format('Y-m');

        if ($this->userToRemove === null) {
            return false;
        }

        $this->openModal('delete-association-member');

        return true;
    }

    public function removeMemberImport(string $memberId): bool
    {
        if (empty($memberId)) {
            return false;
        }

        $this->memberImportIdToRemove = $memberId;
        $this->openModal('delete-member-import');

        return true;
    }

    public function confirmRemoveMemberImport(): void
    {
        if (empty($this->memberImportIdToRemove)) {
            return;
        }

        $memberImport = MemberImport::find($this->memberImportIdToRemove);

        if ($memberImport) {
            $memberImport->delete();
        }

        $this->memberImportIdToRemove = null;
        $this->dispatch('close-modal');
    }

    public function openRemoveAssociationMemberModal(): void
    {
        $this->removeMemberDate = now()->format('Y-m');

        $this->openModal('delete-association-member');
    }

    public function setSelectedRemoveMemberDate(): string
    {
        return now()->format('Y-m');
    }

    /**
     * @return Collection<AssociationMemberListEntry>
     */
    #[Computed]
    public function members(): Collection
    {
        $userQuery = $this->association->members()->withCount('pharmacies')->select([
            'users.id',
            'users.email',
            'users.title',
            'users.first_name',
            'users.last_name',
            'users.created_at',
            DB::raw('CASE WHEN last_login is null THEN \''.AssociationMemberListEntryStatus::RegistrationSent->value.'\' ELSE \''.AssociationMemberListEntryStatus::Active->value.'\'  END  as status'),
            DB::raw('(SELECT COUNT(DISTINCT p.id) FROM pharmacy_role_user pru JOIN pharmacies p ON pru.pharmacy_id = p.id WHERE pru.user_id = users.id AND EXISTS (SELECT 1 FROM subscriptions s WHERE s.pharmacy_id = p.id AND (s.ends_at IS NULL OR (s.ends_at IS NOT NULL AND s.ends_at > '.now()->toDateString().')) AND s.stripe_status NOT IN ("canceled", "incomplete_expired", "incomplete"))) as pharmacies_count'),
            DB::raw('(SELECT MAX(change_at) FROM association_membership_changes amc WHERE amc.user_id = users.id AND amc.canceled_at IS NULL and amc.change_done_at is null) as pending_association_change'),
            DB::raw("'user' as type"),
            DB::raw('NULL as status_description'),
        ])
            ->when(strlen($this->search) > 0,
                fn ($query) => $query->where(function (Builder $whereQuery) {
                    return $whereQuery->where('email', 'like', '%'.$this->search.'%')
                        ->orWhere('title', 'like', '%'.$this->search.'%')
                        ->orWhere('first_name', 'like', '%'.$this->search.'%')
                        ->orWhere('last_name', 'like', '%'.$this->search.'%'
                        );
                }));

        $importQuery = $this->association->memberImports()->withoutTrashed()
            ->select([
                'member_imports.id',
                'member_imports.email',
                'member_imports.title',
                'member_imports.first_name',
                'member_imports.last_name',
                'member_imports.created_at',
                'member_imports.status',
                DB::raw('NULL as pharmacies_count'),
                DB::raw('NULL as pending_association_change'),
                DB::raw("'import' as type"),
                'member_imports.status_description',
            ])
            ->when(
                strlen($this->search) > 0,
                fn ($query) => $query->where(function (Builder $whereQuery) {
                    return $whereQuery->where('email', 'like', '%'.$this->search.'%')
                        ->orWhere('title', 'like', '%'.$this->search.'%')
                        ->orWhere('first_name', 'like', '%'.$this->search.'%')
                        ->orWhere('last_name', 'like', '%'.$this->search.'%');
                })
            );

        // @phpstan-ignore-next-line we ensure via tests that this works
        $combinedQuery = $userQuery->union($importQuery->getBaseQuery());

        // Wrap the union query in a subquery so we can sort it
        $results = DB::table(DB::raw("({$combinedQuery->toSql()}) as combined"))
            // @phpstan-ignore-next-line we ensure via tests that this works
            ->mergeBindings($combinedQuery->getBaseQuery())
            ->orderBy($this->sortBy, $this->sortOrder)
            ->get();

        return $results->map(function ($item) {
            $createdAt = Carbon::parse($item->created_at);
            $status = AssociationMemberListEntryStatus::from($item->status);
            if ($item->type === 'user') {
                return new AssociationMemberListEntry(
                    $item->id,
                    $item->title,
                    $item->first_name,
                    $item->last_name,
                    $item->email,
                    $createdAt,
                    $item->pharmacies_count,
                    $item->pending_association_change ? AssociationMemberListEntryStatus::MarkedForDeletion : $status,
                    $item->pending_association_change ? 'Zum '.Carbon::parse($item->pending_association_change)->format('d.m.Y') : null,
                );
            }

            return new AssociationMemberListEntry(
                $item->id,
                $item->title,
                $item->first_name,
                $item->last_name,
                $item->email,
                $createdAt,
                null,
                $status,
                $item->status_description,
            );
        });
    }

    public function render()
    {
        return view('livewire.association.components.members', [
            'modalName' => self::MODAL_NAME,
            'acceptedFileTypes' => "['".implode("', '", array_values(self::ALLOWED_FILE_TYPES))."']",
            'acceptedFileExtensions' => implode(', ', array_keys(self::ALLOWED_FILE_TYPES)),
        ]);
    }
}
